{"data_mtime": 1750680858, "dep_lines": [6, 7, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["json", "os", "collections", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "io", "json.decoder", "json.encoder", "typing"], "hash": "ec857b230d28418cdf2d3ba79079c8447c3413f8", "id": "add_missing_translations", "ignore_all": false, "interface_hash": "87d0e992a6e61f429fc5f7b84daf62c38edbfe0b", "mtime": 1749815442, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\OneDrive\\coding\\quranic_insights_app\\scripts\\add_missing_translations.py", "plugin_data": null, "size": 9639, "suppressed": [], "version_id": "1.15.0"}