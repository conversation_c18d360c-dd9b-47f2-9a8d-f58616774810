{"data_mtime": 1750680857, "dep_lines": [6, 7, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["json", "os", "collections", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "io", "json.decoder", "json.encoder", "ntpath", "typing"], "hash": "547d19c22b50a23e2355b809f9627ee7e092c48a", "id": "add_auth_translations", "ignore_all": false, "interface_hash": "1f712491bb2e46c35ef39d001994e753debe4bcf", "mtime": 1749815706, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\OneDrive\\coding\\quranic_insights_app\\scripts\\add_auth_translations.py", "plugin_data": null, "size": 23652, "suppressed": [], "version_id": "1.15.0"}