// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'quranic_insight.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

QuranicInsight _$QuranicInsightFromJson(Map<String, dynamic> json) {
  return _QuranicInsight.fromJson(json);
}

/// @nodoc
mixin _$QuranicInsight {
  String get id => throw _privateConstructorUsedError;
  AyahData get sourceAyah => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get explanation => throw _privateConstructorUsedError;
  List<String>? get keyConcepts => throw _privateConstructorUsedError;
  String? get historicalContext => throw _privateConstructorUsedError;
  List<String>? get practicalApplications => throw _privateConstructorUsedError;
  List<String>? get linguisticNuances => throw _privateConstructorUsedError;
  String? get verseReference => throw _privateConstructorUsedError;
  String? get validationStatus => throw _privateConstructorUsedError;
  double? get scholarApprovalRate => throw _privateConstructorUsedError;
  DateTime? get lastUpdated => throw _privateConstructorUsedError;

  /// Serializes this QuranicInsight to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of QuranicInsight
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $QuranicInsightCopyWith<QuranicInsight> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuranicInsightCopyWith<$Res> {
  factory $QuranicInsightCopyWith(
          QuranicInsight value, $Res Function(QuranicInsight) then) =
      _$QuranicInsightCopyWithImpl<$Res, QuranicInsight>;
  @useResult
  $Res call(
      {String id,
      AyahData sourceAyah,
      String title,
      String explanation,
      List<String>? keyConcepts,
      String? historicalContext,
      List<String>? practicalApplications,
      List<String>? linguisticNuances,
      String? verseReference,
      String? validationStatus,
      double? scholarApprovalRate,
      DateTime? lastUpdated});

  $AyahDataCopyWith<$Res> get sourceAyah;
}

/// @nodoc
class _$QuranicInsightCopyWithImpl<$Res, $Val extends QuranicInsight>
    implements $QuranicInsightCopyWith<$Res> {
  _$QuranicInsightCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of QuranicInsight
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? sourceAyah = null,
    Object? title = null,
    Object? explanation = null,
    Object? keyConcepts = freezed,
    Object? historicalContext = freezed,
    Object? practicalApplications = freezed,
    Object? linguisticNuances = freezed,
    Object? verseReference = freezed,
    Object? validationStatus = freezed,
    Object? scholarApprovalRate = freezed,
    Object? lastUpdated = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      sourceAyah: null == sourceAyah
          ? _value.sourceAyah
          : sourceAyah // ignore: cast_nullable_to_non_nullable
              as AyahData,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      explanation: null == explanation
          ? _value.explanation
          : explanation // ignore: cast_nullable_to_non_nullable
              as String,
      keyConcepts: freezed == keyConcepts
          ? _value.keyConcepts
          : keyConcepts // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      historicalContext: freezed == historicalContext
          ? _value.historicalContext
          : historicalContext // ignore: cast_nullable_to_non_nullable
              as String?,
      practicalApplications: freezed == practicalApplications
          ? _value.practicalApplications
          : practicalApplications // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      linguisticNuances: freezed == linguisticNuances
          ? _value.linguisticNuances
          : linguisticNuances // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      verseReference: freezed == verseReference
          ? _value.verseReference
          : verseReference // ignore: cast_nullable_to_non_nullable
              as String?,
      validationStatus: freezed == validationStatus
          ? _value.validationStatus
          : validationStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      scholarApprovalRate: freezed == scholarApprovalRate
          ? _value.scholarApprovalRate
          : scholarApprovalRate // ignore: cast_nullable_to_non_nullable
              as double?,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }

  /// Create a copy of QuranicInsight
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AyahDataCopyWith<$Res> get sourceAyah {
    return $AyahDataCopyWith<$Res>(_value.sourceAyah, (value) {
      return _then(_value.copyWith(sourceAyah: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$QuranicInsightImplCopyWith<$Res>
    implements $QuranicInsightCopyWith<$Res> {
  factory _$$QuranicInsightImplCopyWith(_$QuranicInsightImpl value,
          $Res Function(_$QuranicInsightImpl) then) =
      __$$QuranicInsightImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      AyahData sourceAyah,
      String title,
      String explanation,
      List<String>? keyConcepts,
      String? historicalContext,
      List<String>? practicalApplications,
      List<String>? linguisticNuances,
      String? verseReference,
      String? validationStatus,
      double? scholarApprovalRate,
      DateTime? lastUpdated});

  @override
  $AyahDataCopyWith<$Res> get sourceAyah;
}

/// @nodoc
class __$$QuranicInsightImplCopyWithImpl<$Res>
    extends _$QuranicInsightCopyWithImpl<$Res, _$QuranicInsightImpl>
    implements _$$QuranicInsightImplCopyWith<$Res> {
  __$$QuranicInsightImplCopyWithImpl(
      _$QuranicInsightImpl _value, $Res Function(_$QuranicInsightImpl) _then)
      : super(_value, _then);

  /// Create a copy of QuranicInsight
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? sourceAyah = null,
    Object? title = null,
    Object? explanation = null,
    Object? keyConcepts = freezed,
    Object? historicalContext = freezed,
    Object? practicalApplications = freezed,
    Object? linguisticNuances = freezed,
    Object? verseReference = freezed,
    Object? validationStatus = freezed,
    Object? scholarApprovalRate = freezed,
    Object? lastUpdated = freezed,
  }) {
    return _then(_$QuranicInsightImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      sourceAyah: null == sourceAyah
          ? _value.sourceAyah
          : sourceAyah // ignore: cast_nullable_to_non_nullable
              as AyahData,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      explanation: null == explanation
          ? _value.explanation
          : explanation // ignore: cast_nullable_to_non_nullable
              as String,
      keyConcepts: freezed == keyConcepts
          ? _value._keyConcepts
          : keyConcepts // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      historicalContext: freezed == historicalContext
          ? _value.historicalContext
          : historicalContext // ignore: cast_nullable_to_non_nullable
              as String?,
      practicalApplications: freezed == practicalApplications
          ? _value._practicalApplications
          : practicalApplications // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      linguisticNuances: freezed == linguisticNuances
          ? _value._linguisticNuances
          : linguisticNuances // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      verseReference: freezed == verseReference
          ? _value.verseReference
          : verseReference // ignore: cast_nullable_to_non_nullable
              as String?,
      validationStatus: freezed == validationStatus
          ? _value.validationStatus
          : validationStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      scholarApprovalRate: freezed == scholarApprovalRate
          ? _value.scholarApprovalRate
          : scholarApprovalRate // ignore: cast_nullable_to_non_nullable
              as double?,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$QuranicInsightImpl implements _QuranicInsight {
  const _$QuranicInsightImpl(
      {required this.id,
      required this.sourceAyah,
      required this.title,
      required this.explanation,
      final List<String>? keyConcepts,
      this.historicalContext,
      final List<String>? practicalApplications,
      final List<String>? linguisticNuances,
      this.verseReference,
      this.validationStatus,
      this.scholarApprovalRate,
      this.lastUpdated})
      : _keyConcepts = keyConcepts,
        _practicalApplications = practicalApplications,
        _linguisticNuances = linguisticNuances;

  factory _$QuranicInsightImpl.fromJson(Map<String, dynamic> json) =>
      _$$QuranicInsightImplFromJson(json);

  @override
  final String id;
  @override
  final AyahData sourceAyah;
  @override
  final String title;
  @override
  final String explanation;
  final List<String>? _keyConcepts;
  @override
  List<String>? get keyConcepts {
    final value = _keyConcepts;
    if (value == null) return null;
    if (_keyConcepts is EqualUnmodifiableListView) return _keyConcepts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? historicalContext;
  final List<String>? _practicalApplications;
  @override
  List<String>? get practicalApplications {
    final value = _practicalApplications;
    if (value == null) return null;
    if (_practicalApplications is EqualUnmodifiableListView)
      return _practicalApplications;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _linguisticNuances;
  @override
  List<String>? get linguisticNuances {
    final value = _linguisticNuances;
    if (value == null) return null;
    if (_linguisticNuances is EqualUnmodifiableListView)
      return _linguisticNuances;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? verseReference;
  @override
  final String? validationStatus;
  @override
  final double? scholarApprovalRate;
  @override
  final DateTime? lastUpdated;

  @override
  String toString() {
    return 'QuranicInsight(id: $id, sourceAyah: $sourceAyah, title: $title, explanation: $explanation, keyConcepts: $keyConcepts, historicalContext: $historicalContext, practicalApplications: $practicalApplications, linguisticNuances: $linguisticNuances, verseReference: $verseReference, validationStatus: $validationStatus, scholarApprovalRate: $scholarApprovalRate, lastUpdated: $lastUpdated)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QuranicInsightImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.sourceAyah, sourceAyah) ||
                other.sourceAyah == sourceAyah) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.explanation, explanation) ||
                other.explanation == explanation) &&
            const DeepCollectionEquality()
                .equals(other._keyConcepts, _keyConcepts) &&
            (identical(other.historicalContext, historicalContext) ||
                other.historicalContext == historicalContext) &&
            const DeepCollectionEquality()
                .equals(other._practicalApplications, _practicalApplications) &&
            const DeepCollectionEquality()
                .equals(other._linguisticNuances, _linguisticNuances) &&
            (identical(other.verseReference, verseReference) ||
                other.verseReference == verseReference) &&
            (identical(other.validationStatus, validationStatus) ||
                other.validationStatus == validationStatus) &&
            (identical(other.scholarApprovalRate, scholarApprovalRate) ||
                other.scholarApprovalRate == scholarApprovalRate) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      sourceAyah,
      title,
      explanation,
      const DeepCollectionEquality().hash(_keyConcepts),
      historicalContext,
      const DeepCollectionEquality().hash(_practicalApplications),
      const DeepCollectionEquality().hash(_linguisticNuances),
      verseReference,
      validationStatus,
      scholarApprovalRate,
      lastUpdated);

  /// Create a copy of QuranicInsight
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$QuranicInsightImplCopyWith<_$QuranicInsightImpl> get copyWith =>
      __$$QuranicInsightImplCopyWithImpl<_$QuranicInsightImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$QuranicInsightImplToJson(
      this,
    );
  }
}

abstract class _QuranicInsight implements QuranicInsight {
  const factory _QuranicInsight(
      {required final String id,
      required final AyahData sourceAyah,
      required final String title,
      required final String explanation,
      final List<String>? keyConcepts,
      final String? historicalContext,
      final List<String>? practicalApplications,
      final List<String>? linguisticNuances,
      final String? verseReference,
      final String? validationStatus,
      final double? scholarApprovalRate,
      final DateTime? lastUpdated}) = _$QuranicInsightImpl;

  factory _QuranicInsight.fromJson(Map<String, dynamic> json) =
      _$QuranicInsightImpl.fromJson;

  @override
  String get id;
  @override
  AyahData get sourceAyah;
  @override
  String get title;
  @override
  String get explanation;
  @override
  List<String>? get keyConcepts;
  @override
  String? get historicalContext;
  @override
  List<String>? get practicalApplications;
  @override
  List<String>? get linguisticNuances;
  @override
  String? get verseReference;
  @override
  String? get validationStatus;
  @override
  double? get scholarApprovalRate;
  @override
  DateTime? get lastUpdated;

  /// Create a copy of QuranicInsight
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$QuranicInsightImplCopyWith<_$QuranicInsightImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
