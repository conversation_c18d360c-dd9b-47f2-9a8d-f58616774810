// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'related_verse.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RelatedVerse _$RelatedVerseFromJson(Map<String, dynamic> json) {
  return _RelatedVerse.fromJson(json);
}

/// @nodoc
mixin _$RelatedVerse {
  String get id => throw _privateConstructorUsedError;
  AyahData get targetAyah => throw _privateConstructorUsedError;
  RelationshipType get relationshipType => throw _privateConstructorUsedError;
  double get similarityScore => throw _privateConstructorUsedError;
  String? get explanation => throw _privateConstructorUsedError;
  String? get verseReference => throw _privateConstructorUsedError;

  /// Serializes this RelatedVerse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RelatedVerse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RelatedVerseCopyWith<RelatedVerse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RelatedVerseCopyWith<$Res> {
  factory $RelatedVerseCopyWith(
          RelatedVerse value, $Res Function(RelatedVerse) then) =
      _$RelatedVerseCopyWithImpl<$Res, RelatedVerse>;
  @useResult
  $Res call(
      {String id,
      AyahData targetAyah,
      RelationshipType relationshipType,
      double similarityScore,
      String? explanation,
      String? verseReference});

  $AyahDataCopyWith<$Res> get targetAyah;
}

/// @nodoc
class _$RelatedVerseCopyWithImpl<$Res, $Val extends RelatedVerse>
    implements $RelatedVerseCopyWith<$Res> {
  _$RelatedVerseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RelatedVerse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? targetAyah = null,
    Object? relationshipType = null,
    Object? similarityScore = null,
    Object? explanation = freezed,
    Object? verseReference = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      targetAyah: null == targetAyah
          ? _value.targetAyah
          : targetAyah // ignore: cast_nullable_to_non_nullable
              as AyahData,
      relationshipType: null == relationshipType
          ? _value.relationshipType
          : relationshipType // ignore: cast_nullable_to_non_nullable
              as RelationshipType,
      similarityScore: null == similarityScore
          ? _value.similarityScore
          : similarityScore // ignore: cast_nullable_to_non_nullable
              as double,
      explanation: freezed == explanation
          ? _value.explanation
          : explanation // ignore: cast_nullable_to_non_nullable
              as String?,
      verseReference: freezed == verseReference
          ? _value.verseReference
          : verseReference // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of RelatedVerse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AyahDataCopyWith<$Res> get targetAyah {
    return $AyahDataCopyWith<$Res>(_value.targetAyah, (value) {
      return _then(_value.copyWith(targetAyah: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RelatedVerseImplCopyWith<$Res>
    implements $RelatedVerseCopyWith<$Res> {
  factory _$$RelatedVerseImplCopyWith(
          _$RelatedVerseImpl value, $Res Function(_$RelatedVerseImpl) then) =
      __$$RelatedVerseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      AyahData targetAyah,
      RelationshipType relationshipType,
      double similarityScore,
      String? explanation,
      String? verseReference});

  @override
  $AyahDataCopyWith<$Res> get targetAyah;
}

/// @nodoc
class __$$RelatedVerseImplCopyWithImpl<$Res>
    extends _$RelatedVerseCopyWithImpl<$Res, _$RelatedVerseImpl>
    implements _$$RelatedVerseImplCopyWith<$Res> {
  __$$RelatedVerseImplCopyWithImpl(
      _$RelatedVerseImpl _value, $Res Function(_$RelatedVerseImpl) _then)
      : super(_value, _then);

  /// Create a copy of RelatedVerse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? targetAyah = null,
    Object? relationshipType = null,
    Object? similarityScore = null,
    Object? explanation = freezed,
    Object? verseReference = freezed,
  }) {
    return _then(_$RelatedVerseImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      targetAyah: null == targetAyah
          ? _value.targetAyah
          : targetAyah // ignore: cast_nullable_to_non_nullable
              as AyahData,
      relationshipType: null == relationshipType
          ? _value.relationshipType
          : relationshipType // ignore: cast_nullable_to_non_nullable
              as RelationshipType,
      similarityScore: null == similarityScore
          ? _value.similarityScore
          : similarityScore // ignore: cast_nullable_to_non_nullable
              as double,
      explanation: freezed == explanation
          ? _value.explanation
          : explanation // ignore: cast_nullable_to_non_nullable
              as String?,
      verseReference: freezed == verseReference
          ? _value.verseReference
          : verseReference // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$RelatedVerseImpl implements _RelatedVerse {
  const _$RelatedVerseImpl(
      {required this.id,
      required this.targetAyah,
      required this.relationshipType,
      required this.similarityScore,
      this.explanation,
      this.verseReference});

  factory _$RelatedVerseImpl.fromJson(Map<String, dynamic> json) =>
      _$$RelatedVerseImplFromJson(json);

  @override
  final String id;
  @override
  final AyahData targetAyah;
  @override
  final RelationshipType relationshipType;
  @override
  final double similarityScore;
  @override
  final String? explanation;
  @override
  final String? verseReference;

  @override
  String toString() {
    return 'RelatedVerse(id: $id, targetAyah: $targetAyah, relationshipType: $relationshipType, similarityScore: $similarityScore, explanation: $explanation, verseReference: $verseReference)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RelatedVerseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.targetAyah, targetAyah) ||
                other.targetAyah == targetAyah) &&
            (identical(other.relationshipType, relationshipType) ||
                other.relationshipType == relationshipType) &&
            (identical(other.similarityScore, similarityScore) ||
                other.similarityScore == similarityScore) &&
            (identical(other.explanation, explanation) ||
                other.explanation == explanation) &&
            (identical(other.verseReference, verseReference) ||
                other.verseReference == verseReference));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, targetAyah, relationshipType,
      similarityScore, explanation, verseReference);

  /// Create a copy of RelatedVerse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RelatedVerseImplCopyWith<_$RelatedVerseImpl> get copyWith =>
      __$$RelatedVerseImplCopyWithImpl<_$RelatedVerseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RelatedVerseImplToJson(
      this,
    );
  }
}

abstract class _RelatedVerse implements RelatedVerse {
  const factory _RelatedVerse(
      {required final String id,
      required final AyahData targetAyah,
      required final RelationshipType relationshipType,
      required final double similarityScore,
      final String? explanation,
      final String? verseReference}) = _$RelatedVerseImpl;

  factory _RelatedVerse.fromJson(Map<String, dynamic> json) =
      _$RelatedVerseImpl.fromJson;

  @override
  String get id;
  @override
  AyahData get targetAyah;
  @override
  RelationshipType get relationshipType;
  @override
  double get similarityScore;
  @override
  String? get explanation;
  @override
  String? get verseReference;

  /// Create a copy of RelatedVerse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RelatedVerseImplCopyWith<_$RelatedVerseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
