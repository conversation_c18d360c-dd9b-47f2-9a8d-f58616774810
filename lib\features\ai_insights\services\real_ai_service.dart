import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '/core/models/quranic_insight.dart';
import '/core/models/ayah_data.dart';
import '/core/models/related_verse.dart';
import '/core/models/relationship_type.dart';
import '/core/services/ai/openai_service.dart';
import '/core/services/ai/claude_service.dart';
import '/core/config/supabase_config.dart';
import '/core/providers/supabase_provider.dart';

class RealAIService {
  final OpenAIService _openAIService;
  final ClaudeService _claudeService;
  final SupabaseClient _supabase;

  RealAIService({
    required OpenAIService openAIService,
    required ClaudeService claudeService,
    required SupabaseClient supabase,
  })  : _openAIService = openAIService,
        _claudeService = claudeService,
        _supabase = supabase;

  /// Generate insight for a specific verse using real AI
  Future<QuranicInsight> generateInsight({
    required int surahNumber,
    required int ayahNumber,
    required String ayahText,
    required String context,
  }) async {
    try {
      // Check cache first
      final cachedInsight = await _getCachedInsight(surahNumber, ayahNumber);
      if (cachedInsight != null) {
        return cachedInsight;
      }

      // Use Claude for Arabic content as it's optimized for it
      final insightData = await _claudeService.generateVerseInsight(
        ayahText: ayahText,
        surahName: _getSurahName(surahNumber),
        ayahNumber: ayahNumber,
        userContext: context,
      );

      // Create the insight object
      final sourceAyah = AyahData(
        id: '${surahNumber}_$ayahNumber',
        surahNumber: surahNumber,
        ayahNumberInSurah: ayahNumber,
        text: ayahText,
        textUthmani: ayahText,
        textSimple: ayahText,
        juz: 1, // Default value, should be fetched from actual data
        page: 1, // Default value, should be fetched from actual data
        lineStart: 1, // Default value
        lineEnd: 1, // Default value
      );

      final insight = QuranicInsight(
        id: 'insight_${surahNumber}_$ayahNumber',
        sourceAyah: sourceAyah,
        verseReference: '$surahNumber:$ayahNumber',
        title: insightData['title'] ?? 'Quranic Insight',
        explanation: insightData['explanation'] ?? '',
        keyConcepts: List<String>.from(insightData['key_concepts'] ?? []),
        practicalApplications: List<String>.from(insightData['practical_applications'] ?? []),
        historicalContext: insightData['historical_context'],
        linguisticNuances: List<String>.from(insightData['linguistic_nuances'] ?? []),
        validationStatus: 'pending',
        scholarApprovalRate: 0.0,
        lastUpdated: DateTime.now(),
      );

      // Cache the insight
      await _cacheInsight(insight, surahNumber, ayahNumber);

      return insight;
    } catch (e) {
      print('Error generating real AI insight: $e');
      rethrow;
    }
  }

  /// Get cached insight from Supabase
  Future<QuranicInsight?> _getCachedInsight(int surahNumber, int ayahNumber) async {
    try {
      final response = await _supabase
          .from('ai_insights')
          .select()
          .eq('surah_number', surahNumber)
          .eq('ayah_number', ayahNumber)
          .single();

      if (response != null) {
        // Parse cached data
        final content = jsonDecode(response['content'] ?? '{}');
        final metadata = jsonDecode(response['metadata'] ?? '{}');
        
        // Create a dummy sourceAyah for cached insights
        final sourceAyah = AyahData(
          id: response['verse_reference'] ?? 'unknown',
          surahNumber: response['surah_number'] ?? 1,
          ayahNumberInSurah: response['ayah_number'] ?? 1,
          text: 'Cached verse text',
          textUthmani: 'Cached verse text',
          textSimple: 'Cached verse text',
          juz: 1,
          page: 1,
          lineStart: 1,
          lineEnd: 1,
        );

        return QuranicInsight(
          id: response['id'],
          sourceAyah: sourceAyah,
          verseReference: response['verse_reference'],
          title: content['title'] ?? 'Cached Insight',
          explanation: content['explanation'] ?? '',
          keyConcepts: List<String>.from(content['key_concepts'] ?? []),
          practicalApplications: List<String>.from(content['practical_applications'] ?? []),
          historicalContext: content['historical_context'],
          linguisticNuances: List<String>.from(content['linguistic_nuances'] ?? []),
          validationStatus: response['validation_status'] ?? 'pending',
          scholarApprovalRate: (metadata['scholar_approval_rate'] ?? 0).toDouble(),
          lastUpdated: DateTime.parse(response['updated_at']),
        );
      }
    } catch (e) {
      print('Error fetching cached insight: $e');
    }
    return null;
  }

  /// Cache insight in Supabase
  Future<void> _cacheInsight(QuranicInsight insight, int surahNumber, int ayahNumber) async {
    try {
      final content = {
        'title': insight.title,
        'explanation': insight.explanation,
        'key_concepts': insight.keyConcepts,
        'practical_applications': insight.practicalApplications,
        'historical_context': insight.historicalContext,
        'linguistic_nuances': insight.linguisticNuances,
      };

      final metadata = {
        'scholar_approval_rate': insight.scholarApprovalRate,
        'generated_by': 'claude',
      };

      await _supabase.from('ai_insights').upsert({
        'verse_reference': insight.verseReference,
        'surah_number': surahNumber,
        'ayah_number': ayahNumber,
        'content': jsonEncode(content),
        'metadata': jsonEncode(metadata),
        'cache_key': 'v1_${surahNumber}_$ayahNumber',
        'expires_at': DateTime.now().add(const Duration(days: 30)).toIso8601String(),
        'validation_status': insight.validationStatus,
      }, onConflict: 'surah_number,ayah_number');
    } catch (e) {
      print('Error caching insight: $e');
    }
  }

  /// Extract related concepts using AI
  Future<List<String>> extractRelatedConcepts(String topic) async {
    try {
      return await _claudeService.extractRelatedConcepts(topic);
    } catch (e) {
      print('Error extracting concepts: $e');
      return [];
    }
  }

  /// Generate thematic analysis
  Future<Map<String, dynamic>> generateThematicAnalysis(String theme) async {
    try {
      return await _claudeService.generateThematicAnalysis(theme);
    } catch (e) {
      print('Error generating thematic analysis: $e');
      return {};
    }
  }

  /// Find semantically related verses using OpenAI embeddings
  Future<List<RelatedVerse>> findRelatedVerses({
    required String verseText,
    required List<AyahData> candidateVerses,
    int maxResults = 5,
  }) async {
    try {
      // Generate embedding for the query verse
      final queryEmbedding = await _openAIService.generateEmbedding(verseText);
      if (queryEmbedding.isEmpty) return [];

      // Find similar verses
      final semanticMatches = await _openAIService.findSemanticSimilarities(
        queryText: verseText,
        candidateTexts: candidateVerses.map((v) => v.textUthmani).toList(),
        minimumSimilarity: 0.7,
        maxResults: maxResults,
      );

      // Convert to RelatedVerse objects
      return semanticMatches.map((match) {
        final verse = candidateVerses[match.index];
        return RelatedVerse(
          id: 'related_${verse.surahNumber}_${verse.ayahNumberInSurah}',
          targetAyah: verse,
          verseReference: '${verse.surahNumber}:${verse.ayahNumberInSurah}',
          relationshipType: _determineRelationshipType(match.similarity),
          similarityScore: match.similarity,
        );
      }).toList();
    } catch (e) {
      print('Error finding related verses: $e');
      return [];
    }
  }

  RelationshipType _determineRelationshipType(double similarity) {
    if (similarity > 0.9) return RelationshipType.continuation;
    if (similarity > 0.8) return RelationshipType.thematicallyRelated;
    if (similarity > 0.7) return RelationshipType.elaboration;
    return RelationshipType.thematicallyRelated;
  }

  String _getSurahName(int surahNumber) {
    // Basic surah names - you might want to expand this
    final surahNames = {
      1: 'Al-Fatihah',
      2: 'Al-Baqarah',
      3: 'Ali \'Imran',
      4: 'An-Nisa',
      5: 'Al-Ma\'idah',
      // Add more as needed
    };
    return surahNames[surahNumber] ?? 'Surah $surahNumber';
  }
}

/// Provider for Real AI Service
final realAIServiceProvider = Provider<RealAIService>((ref) {
  final openAIService = ref.watch(openAIServiceProvider);
  final claudeService = ref.watch(claudeServiceProvider);
  final supabase = ref.watch(supabaseClientProvider);
  
  return RealAIService(
    openAIService: openAIService,
    claudeService: claudeService,
    supabase: supabase,
  );
});