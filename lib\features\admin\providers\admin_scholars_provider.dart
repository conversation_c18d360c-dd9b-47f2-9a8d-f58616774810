import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '/core/config/supabase_config.dart';
import '/core/providers/supabase_provider.dart';

// Scholar model
class Scholar {
  final String id;
  final String? userId;
  final String fullName;
  final String email;
  final String? phone;
  final List<String> credentials;
  final List<String> expertiseAreas;
  final String? institution;
  final String? bio;
  final List<String> languages;
  final String verificationStatus;
  final DateTime? verifiedAt;
  final String? verifiedBy;
  final String? verificationNotes;
  final bool isActive;
  final bool canReviewSensitiveContent;
  final int maxReviewsPerWeek;
  final DateTime createdAt;
  final DateTime updatedAt;
  final ScholarMetrics? metrics;

  Scholar({
    required this.id,
    this.userId,
    required this.fullName,
    required this.email,
    this.phone,
    required this.credentials,
    required this.expertiseAreas,
    this.institution,
    this.bio,
    required this.languages,
    required this.verificationStatus,
    this.verifiedAt,
    this.verifiedBy,
    this.verificationNotes,
    required this.isActive,
    required this.canReviewSensitiveContent,
    required this.maxReviewsPerWeek,
    required this.createdAt,
    required this.updatedAt,
    this.metrics,
  });

  factory Scholar.fromJson(Map<String, dynamic> json) {
    return Scholar(
      id: json['id'],
      userId: json['user_id'],
      fullName: json['full_name'],
      email: json['email'],
      phone: json['phone'],
      credentials: List<String>.from(json['credentials'] ?? []),
      expertiseAreas: List<String>.from(json['expertise_areas'] ?? []),
      institution: json['institution'],
      bio: json['bio'],
      languages: List<String>.from(json['languages'] ?? ['arabic', 'english']),
      verificationStatus: json['verification_status'],
      verifiedAt: json['verified_at'] != null ? DateTime.parse(json['verified_at']) : null,
      verifiedBy: json['verified_by'],
      verificationNotes: json['verification_notes'],
      isActive: json['is_active'] ?? true,
      canReviewSensitiveContent: json['can_review_sensitive_content'] ?? false,
      maxReviewsPerWeek: json['max_reviews_per_week'] ?? 20,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      metrics: json['scholar_metrics'] != null && (json['scholar_metrics'] as List).isNotEmpty
          ? ScholarMetrics.fromJson(json['scholar_metrics'][0])
          : null,
    );
  }
}

// Scholar metrics model
class ScholarMetrics {
  final String id;
  final String scholarId;
  final int totalReviews;
  final int approvedCount;
  final int rejectedCount;
  final int revisionCount;
  final int escalationCount;
  final int reviewsThisWeek;
  final int reviewsThisMonth;
  final double? averageReviewTimeHours;
  final double? averageConfidenceScore;
  final double? averageTheologicalAccuracy;
  final double? averageLinguisticQuality;
  final double? agreementRate;
  final double? peerRating;
  final double? reliabilityScore;
  final DateTime? lastReviewAt;

  ScholarMetrics({
    required this.id,
    required this.scholarId,
    required this.totalReviews,
    required this.approvedCount,
    required this.rejectedCount,
    required this.revisionCount,
    required this.escalationCount,
    required this.reviewsThisWeek,
    required this.reviewsThisMonth,
    this.averageReviewTimeHours,
    this.averageConfidenceScore,
    this.averageTheologicalAccuracy,
    this.averageLinguisticQuality,
    this.agreementRate,
    this.peerRating,
    this.reliabilityScore,
    this.lastReviewAt,
  });

  factory ScholarMetrics.fromJson(Map<String, dynamic> json) {
    return ScholarMetrics(
      id: json['id'],
      scholarId: json['scholar_id'],
      totalReviews: json['total_reviews'] ?? 0,
      approvedCount: json['approved_count'] ?? 0,
      rejectedCount: json['rejected_count'] ?? 0,
      revisionCount: json['revision_count'] ?? 0,
      escalationCount: json['escalation_count'] ?? 0,
      reviewsThisWeek: json['reviews_this_week'] ?? 0,
      reviewsThisMonth: json['reviews_this_month'] ?? 0,
      averageReviewTimeHours: json['average_review_time_hours']?.toDouble(),
      averageConfidenceScore: json['average_confidence_score']?.toDouble(),
      averageTheologicalAccuracy: json['average_theological_accuracy']?.toDouble(),
      averageLinguisticQuality: json['average_linguistic_quality']?.toDouble(),
      agreementRate: json['agreement_rate']?.toDouble(),
      peerRating: json['peer_rating']?.toDouble(),
      reliabilityScore: json['reliability_score']?.toDouble(),
      lastReviewAt: json['last_review_at'] != null ? DateTime.parse(json['last_review_at']) : null,
    );
  }
}

// Provider for managing scholars
class AdminScholarsNotifier extends AsyncNotifier<List<Scholar>> {
  late final SupabaseClient _supabase;

  @override
  Future<List<Scholar>> build() async {
    _supabase = ref.read(supabaseClientProvider);
    return await _fetchScholars();
  }

  Future<List<Scholar>> _fetchScholars() async {
    try {
      final response = await _supabase
          .from('scholars')
          .select('*, scholar_metrics(*)')
          .order('created_at', ascending: false);

      return (response as List)
          .map((json) => Scholar.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch scholars: $e');
    }
  }

  Future<void> addScholar({
    required String fullName,
    required String email,
    String? phone,
    String? institution,
    String? bio,
    required List<String> expertiseAreas,
    required List<String> languages,
  }) async {
    try {
      await _supabase.from('scholars').insert({
        'full_name': fullName,
        'email': email,
        'phone': phone,
        'institution': institution,
        'bio': bio,
        'expertise_areas': expertiseAreas,
        'languages': languages,
        'verification_status': 'pending',
      });

      // Refresh the list
      ref.invalidateSelf();
    } catch (e) {
      throw Exception('Failed to add scholar: $e');
    }
  }

  Future<void> verifyScholar(String scholarId) async {
    try {
      final currentUser = _supabase.auth.currentUser;
      await _supabase.from('scholars').update({
        'verification_status': 'verified',
        'verified_at': DateTime.now().toIso8601String(),
        'verified_by': currentUser?.id,
      }).eq('id', scholarId);

      // Refresh the list
      ref.invalidateSelf();
    } catch (e) {
      throw Exception('Failed to verify scholar: $e');
    }
  }

  Future<void> rejectScholar(String scholarId, String reason) async {
    try {
      final currentUser = _supabase.auth.currentUser;
      await _supabase.from('scholars').update({
        'verification_status': 'rejected',
        'verified_at': DateTime.now().toIso8601String(),
        'verified_by': currentUser?.id,
        'verification_notes': reason,
      }).eq('id', scholarId);

      // Refresh the list
      ref.invalidateSelf();
    } catch (e) {
      throw Exception('Failed to reject scholar: $e');
    }
  }

  Future<void> suspendScholar(String scholarId) async {
    try {
      await _supabase.from('scholars').update({
        'verification_status': 'suspended',
        'is_active': false,
      }).eq('id', scholarId);

      // Refresh the list
      ref.invalidateSelf();
    } catch (e) {
      throw Exception('Failed to suspend scholar: $e');
    }
  }

  Future<void> activateScholar(String scholarId) async {
    try {
      await _supabase.from('scholars').update({
        'is_active': true,
      }).eq('id', scholarId);

      // Refresh the list
      ref.invalidateSelf();
    } catch (e) {
      throw Exception('Failed to activate scholar: $e');
    }
  }

  Future<void> updateScholar(String scholarId, Map<String, dynamic> updates) async {
    try {
      await _supabase.from('scholars')
          .update(updates)
          .eq('id', scholarId);

      // Refresh the list
      ref.invalidateSelf();
    } catch (e) {
      throw Exception('Failed to update scholar: $e');
    }
  }

  Future<void> deleteScholar(String scholarId) async {
    try {
      await _supabase.from('scholars')
          .delete()
          .eq('id', scholarId);

      // Refresh the list
      ref.invalidateSelf();
    } catch (e) {
      throw Exception('Failed to delete scholar: $e');
    }
  }
}

// Provider
final adminScholarsProvider = AsyncNotifierProvider<AdminScholarsNotifier, List<Scholar>>(
  AdminScholarsNotifier.new,
);

// Provider for scholar statistics
final scholarStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final supabase = ref.read(supabaseClientProvider);
  
  try {
    // Get total count by status
    final statusCounts = await supabase
        .from('scholars')
        .select('verification_status')
        .then((data) {
          final counts = <String, int>{
            'total': 0,
            'pending': 0,
            'verified': 0,
            'rejected': 0,
            'suspended': 0,
          };
          
          for (final record in data as List) {
            counts['total'] = counts['total']! + 1;
            final status = record['verification_status'];
            if (counts.containsKey(status)) {
              counts[status] = counts[status]! + 1;
            }
          }
          
          return counts;
        });

    // Get active scholars count
    final activeCount = await supabase
        .from('scholars')
        .select('id')
        .eq('is_active', true)
        .count();

    // Get review statistics
    final reviewStats = await supabase
        .from('scholar_reviews')
        .select('decision')
        .then((data) {
          final stats = <String, int>{
            'total_reviews': 0,
            'approved': 0,
            'rejected': 0,
            'needs_revision': 0,
          };
          
          for (final record in data as List) {
            stats['total_reviews'] = stats['total_reviews']! + 1;
            final decision = record['decision'];
            if (stats.containsKey(decision)) {
              stats[decision] = stats[decision]! + 1;
            }
          }
          
          return stats;
        });

    return {
      ...statusCounts,
      'active_count': activeCount,
      ...reviewStats,
    };
  } catch (e) {
    throw Exception('Failed to fetch scholar statistics: $e');
  }
});

// Provider for pending review assignments
final pendingReviewAssignmentsProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  final supabase = ref.read(supabaseClientProvider);
  
  try {
    final response = await supabase
        .from('review_assignments')
        .select('*, scholars(full_name, email), ai_insights(title, verse_reference)')
        .inFilter('status', ['pending', 'in_progress'])
        .order('due_date', ascending: true)
        .limit(10);

    return List<Map<String, dynamic>>.from(response);
  } catch (e) {
    throw Exception('Failed to fetch pending assignments: $e');
  }
});