import 'package:freezed_annotation/freezed_annotation.dart';
import '/core/models/ayah_data.dart';
import '/core/models/relationship_type.dart';

part 'related_verse.freezed.dart';
part 'related_verse.g.dart';

@freezed
class RelatedVerse with _$RelatedVerse {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory RelatedVerse({
    required String id,
    required AyahData targetAyah,
    required RelationshipType relationshipType,
    required double similarityScore,
    String? explanation,
    String? verseReference,
  }) = _RelatedVerse;

  factory RelatedVerse.fromJson(Map<String, dynamic> json) =>
      _$RelatedVerseFromJson(json);
}
