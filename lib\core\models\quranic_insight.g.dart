// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quranic_insight.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$QuranicInsightImpl _$$QuranicInsightImplFromJson(Map<String, dynamic> json) =>
    _$QuranicInsightImpl(
      id: json['id'] as String,
      sourceAyah:
          AyahData.fromJson(json['source_ayah'] as Map<String, dynamic>),
      title: json['title'] as String,
      explanation: json['explanation'] as String,
      keyConcepts: (json['key_concepts'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      historicalContext: json['historical_context'] as String?,
      practicalApplications: (json['practical_applications'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      linguisticNuances: (json['linguistic_nuances'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      verseReference: json['verse_reference'] as String?,
      validationStatus: json['validation_status'] as String?,
      scholarApprovalRate: (json['scholar_approval_rate'] as num?)?.toDouble(),
      lastUpdated: json['last_updated'] == null
          ? null
          : DateTime.parse(json['last_updated'] as String),
    );

Map<String, dynamic> _$$QuranicInsightImplToJson(
        _$QuranicInsightImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'source_ayah': instance.sourceAyah,
      'title': instance.title,
      'explanation': instance.explanation,
      'key_concepts': instance.keyConcepts,
      'historical_context': instance.historicalContext,
      'practical_applications': instance.practicalApplications,
      'linguistic_nuances': instance.linguisticNuances,
      'verse_reference': instance.verseReference,
      'validation_status': instance.validationStatus,
      'scholar_approval_rate': instance.scholarApprovalRate,
      'last_updated': instance.lastUpdated?.toIso8601String(),
    };
