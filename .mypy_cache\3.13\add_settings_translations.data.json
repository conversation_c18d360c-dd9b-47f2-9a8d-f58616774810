{".class": "MypyFile", "_fullname": "add_settings_translations", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "SETTINGS_TRANSLATIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "add_settings_translations.SETTINGS_TRANSLATIONS", "name": "SETTINGS_TRANSLATIONS", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "add_settings_translations.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "add_settings_translations.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "add_settings_translations.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "add_settings_translations.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "add_settings_translations.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "add_settings_translations.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "add_settings_translations.main", "name": "main", "type": null}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "update_language_file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["lang_code", "translations"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "add_settings_translations.update_language_file", "name": "update_language_file", "type": null}}}, "path": "C:\\Users\\<USER>\\OneDrive\\coding\\quranic_insights_app\\scripts\\add_settings_translations.py"}