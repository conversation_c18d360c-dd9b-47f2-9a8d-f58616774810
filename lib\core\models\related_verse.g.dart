// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'related_verse.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RelatedVerseImpl _$$RelatedVerseImplFromJson(Map<String, dynamic> json) =>
    _$RelatedVerseImpl(
      id: json['id'] as String,
      targetAyah:
          AyahData.fromJson(json['target_ayah'] as Map<String, dynamic>),
      relationshipType:
          $enumDecode(_$RelationshipTypeEnumMap, json['relationship_type']),
      similarityScore: (json['similarity_score'] as num).toDouble(),
      explanation: json['explanation'] as String?,
      verseReference: json['verse_reference'] as String?,
    );

Map<String, dynamic> _$$RelatedVerseImplToJson(_$RelatedVerseImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'target_ayah': instance.targetAyah,
      'relationship_type':
          _$RelationshipTypeEnumMap[instance.relationshipType]!,
      'similarity_score': instance.similarityScore,
      'explanation': instance.explanation,
      'verse_reference': instance.verseReference,
    };

const _$RelationshipTypeEnumMap = {
  RelationshipType.thematicallyRelated: 'thematically_related',
  RelationshipType.continuation: 'continuation',
  RelationshipType.contrast: 'contrast',
  RelationshipType.elaboration: 'elaboration',
  RelationshipType.historicalContext: 'historical_context',
  RelationshipType.linguisticNuance: 'linguistic_nuance',
  RelationshipType.practicalApplication: 'practical_application',
};
