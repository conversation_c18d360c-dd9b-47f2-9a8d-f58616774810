import 'package:freezed_annotation/freezed_annotation.dart';
import '/core/models/ayah_data.dart';

part 'quranic_insight.freezed.dart';
part 'quranic_insight.g.dart';

@freezed
class QuranicInsight with _$QuranicInsight {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory QuranicInsight({
    required String id,
    required AyahData sourceAyah,
    required String title,
    required String explanation,
    List<String>? keyConcepts,
    String? historicalContext,
    List<String>? practicalApplications,
    List<String>? linguisticNuances,
    String? verseReference,
    String? validationStatus,
    double? scholarApprovalRate,
    DateTime? lastUpdated,
    // Add other relevant fields as needed
  }) = _QuranicInsight;

  factory QuranicInsight.fromJson(Map<String, dynamic> json) =>
      _$QuranicInsightFromJson(json);
}
