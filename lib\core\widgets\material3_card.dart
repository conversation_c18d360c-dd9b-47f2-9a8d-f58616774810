import 'package:flutter/material.dart';

/// Material 3 Card with proper elevation and surface tinting
class Material3Card extends StatelessWidget {
  final Widget child;
  final double elevation;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final VoidCallback? onTap;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;

  const Material3Card({
    super.key,
    required this.child,
    this.elevation = 1,
    this.padding,
    this.margin,
    this.onTap,
    this.backgroundColor,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // Material 3 elevation levels and their corresponding tint opacity
    final tintOpacity = switch (elevation) {
      0 => 0.0,
      1 => isDark ? 0.05 : 0.05,
      2 => isDark ? 0.08 : 0.08,
      3 => isDark ? 0.11 : 0.11,
      4 => isDark ? 0.12 : 0.12,
      5 => isDark ? 0.14 : 0.14,
      _ => isDark ? 0.14 : 0.14,
    };

    // Calculate surface tint color
    final surfaceTintColor = theme.colorScheme.surfaceTint;
    final baseColor = backgroundColor ?? theme.colorScheme.surface;
    final tintedColor = Color.alphaBlend(
      surfaceTintColor.withValues(alpha: tintOpacity),
      baseColor,
    );

    final materialCard = Material(
      color: tintedColor,
      borderRadius: borderRadius ?? BorderRadius.circular(12),
      elevation: elevation,
      shadowColor: theme.colorScheme.shadow.withValues(alpha: 0.3),
      surfaceTintColor: Colors.transparent, // We handle tinting manually
      child: InkWell(
        onTap: onTap,
        borderRadius: borderRadius ?? BorderRadius.circular(12),
        child: Container(
          padding: padding,
          child: child,
        ),
      ),
    );

    if (margin != null) {
      return Container(
        margin: margin,
        child: materialCard,
      );
    }

    return materialCard;
  }
}

/// Material 3 Elevated Container for non-interactive surfaces
class Material3Container extends StatelessWidget {
  final Widget child;
  final double elevation;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;

  const Material3Container({
    super.key,
    required this.child,
    this.elevation = 1,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    // Material 3 elevation levels and their corresponding tint opacity
    final tintOpacity = switch (elevation) {
      0 => 0.0,
      1 => isDark ? 0.05 : 0.05,
      2 => isDark ? 0.08 : 0.08,
      3 => isDark ? 0.11 : 0.11,
      4 => isDark ? 0.12 : 0.12,
      5 => isDark ? 0.14 : 0.14,
      _ => isDark ? 0.14 : 0.14,
    };
    
    // Calculate surface tint color
    final surfaceTintColor = theme.colorScheme.surfaceTint;
    final baseColor = backgroundColor ?? theme.colorScheme.surface;
    final tintedColor = Color.alphaBlend(
      surfaceTintColor.withValues(alpha: tintOpacity),
      baseColor,
    );
    
    return Container(
      padding: padding,
      decoration: BoxDecoration(
        color: tintedColor,
        borderRadius: borderRadius ?? BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: elevation * 2,
            offset: Offset(0, elevation),
          ),
        ],
      ),
      child: child,
    );
  }
}